#!/usr/bin/env python3
"""
Quick Setup Verification Script
Verifies that all components are ready for testing and APK build
"""

import os
import sys
import subprocess
import json
import requests
from pathlib import Path

def print_status(message, status):
    """Print status with appropriate emoji"""
    emoji = "✅" if status else "❌"
    print(f"{emoji} {message}")

def check_backend_files():
    """Check if backend files are properly updated"""
    print("🔍 Checking backend files...")
    
    required_files = [
        'project/src/controllers/aiAnalysisController.js',
        'project/src/services/bertModelService.js',
        'data/mental_health_dataset.js',
        'data/comprehensive_mental_health_dataset.js',
        'data/extended_mental_health_dataset.js',
        'data/specialized_mental_health_dataset.js'
    ]
    
    all_exist = True
    for file_path in required_files:
        exists = Path(file_path).exists()
        print_status(f"Backend file: {file_path}", exists)
        if not exists:
            all_exist = False
    
    return all_exist

def check_frontend_files():
    """Check if frontend files are updated"""
    print("\n🔍 Checking frontend files...")
    
    # Check if AI analysis screen has enhanced methods
    ai_screen_path = Path('lib/ai_analysis_screen.dart')
    if ai_screen_path.exists():
        content = ai_screen_path.read_text()
        has_enhanced_methods = (
            '_buildEnhancedRecommendations' in content and
            '_buildModelInfoCard' in content and
            '_buildEnhancedRiskCard' in content
        )
        print_status("Frontend enhanced methods", has_enhanced_methods)
        return has_enhanced_methods
    else:
        print_status("AI Analysis Screen file", False)
        return False

def check_model_files():
    """Check if model files exist"""
    print("\n🔍 Checking AI model files...")
    
    model_files = [
        'model/dataset_converter.py',
        'model/enhanced_bert_training.py',
        'model/Combined Data.csv'
    ]
    
    all_exist = True
    for file_path in model_files:
        exists = Path(file_path).exists()
        print_status(f"Model file: {file_path}", exists)
        if not exists:
            all_exist = False
    
    # Check if enhanced model exists (optional)
    enhanced_model_path = Path('model/enhanced_mental_health_bert')
    if enhanced_model_path.exists():
        print_status("Enhanced BERT model", True)
    else:
        print_status("Enhanced BERT model (will be created)", False)
    
    return all_exist

def check_flutter_dependencies():
    """Check Flutter dependencies"""
    print("\n🔍 Checking Flutter dependencies...")
    
    pubspec_path = Path('pubspec.yaml')
    if pubspec_path.exists():
        print_status("pubspec.yaml exists", True)
        
        # Check if flutter command works
        try:
            result = subprocess.run(['flutter', 'doctor'], 
                                  capture_output=True, text=True, timeout=30)
            flutter_ok = result.returncode == 0
            print_status("Flutter doctor check", flutter_ok)
            return flutter_ok
        except (subprocess.TimeoutExpired, FileNotFoundError):
            print_status("Flutter command available", False)
            return False
    else:
        print_status("pubspec.yaml exists", False)
        return False

def test_backend_connection():
    """Test if backend can be reached"""
    print("\n🔍 Testing backend connection...")
    
    backend_urls = [
        'http://localhost:3000',
        'http://***********:3000',
        'http://127.0.0.1:3000'
    ]
    
    for url in backend_urls:
        try:
            response = requests.get(f"{url}/api/health", timeout=3)
            if response.status_code == 200:
                print_status(f"Backend accessible at {url}", True)
                return url
        except requests.exceptions.RequestException:
            continue
    
    print_status("Backend server running", False)
    return None

def provide_setup_instructions():
    """Provide setup instructions based on what's missing"""
    print("\n📋 Setup Instructions:")
    print("="*50)
    
    print("\n1. 🧠 Enhanced AI Model Setup:")
    print("   Run: python setup_enhanced_ai.py")
    print("   This will:")
    print("   - Convert your datasets to proper format")
    print("   - Train the enhanced BERT model")
    print("   - Set up all integrations")
    
    print("\n2. 🚀 Backend Server:")
    print("   cd project")
    print("   npm install")
    print("   npm start")
    
    print("\n3. 📱 Flutter Dependencies:")
    print("   flutter clean")
    print("   flutter pub get")
    
    print("\n4. 🧪 Test & Build APK:")
    print("   python test_and_build_apk.py")

def main():
    """Main verification function"""
    print("🔍 MindEase Setup Verification")
    print("="*40)
    
    # Check all components
    backend_ok = check_backend_files()
    frontend_ok = check_frontend_files()
    model_ok = check_model_files()
    flutter_ok = check_flutter_dependencies()
    
    # Test backend connection
    backend_url = test_backend_connection()
    
    # Summary
    print("\n📊 Setup Summary:")
    print("="*30)
    print_status("Backend files ready", backend_ok)
    print_status("Frontend updated", frontend_ok)
    print_status("Model files present", model_ok)
    print_status("Flutter ready", flutter_ok)
    print_status("Backend server running", backend_url is not None)
    
    all_ready = backend_ok and frontend_ok and model_ok and flutter_ok
    
    if all_ready and backend_url:
        print("\n🎉 Everything is ready!")
        print("✅ You can now run: python test_and_build_apk.py")
    elif all_ready:
        print("\n⚠️  Almost ready!")
        print("   Just start your backend server and you're good to go!")
        print("   cd project && npm start")
    else:
        print("\n⚠️  Setup needed!")
        provide_setup_instructions()
    
    return all_ready

if __name__ == "__main__":
    main()
