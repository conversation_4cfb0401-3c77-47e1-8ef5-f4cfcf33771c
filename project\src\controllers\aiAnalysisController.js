const { JournalEntry, User } = require('../models');
const axios = require('axios');
const { mentalHealthDataset, contextPatterns, riskKeywords } = require('../../data/mental_health_dataset');
const { comprehensiveMentalHealthDataset } = require('../../data/comprehensive_mental_health_dataset');
const { extendedMentalHealthDataset } = require('../../data/extended_mental_health_dataset');
const { specializedMentalHealthDataset } = require('../../data/specialized_mental_health_dataset');
const bertModelService = require('../services/bertModelService');

class AIAnalysisController {
  // Analyze journal entry using AI model
  static async analyzeJournalEntry(req, res, next) {
    try {
      const { journalEntryId, userId } = req.params;
      const { content } = req.body;

      // Validate user
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Get journal entry if ID provided, otherwise use content from body
      let journalContent = content;
      if (journalEntryId) {
        const journalEntry = await JournalEntry.findById(journalEntryId);
        if (!journalEntry) {
          return res.status(404).json({ message: 'Journal entry not found' });
        }
        journalContent = journalEntry.content;
      }

      if (!journalContent) {
        return res.status(400).json({ message: 'No content to analyze' });
      }

      // Perform AI analysis
      const analysis = await AIAnalysisController.performAIAnalysis(journalContent);

      // Save analysis results
      const analysisResult = {
        userId,
        journalEntryId: journalEntryId || null,
        content: journalContent,
        analysis,
        timestamp: new Date(),
      };

      res.status(200).json({
        message: 'Analysis completed successfully',
        result: analysisResult,
      });

    } catch (error) {
      console.error('Error in AI analysis:', error);
      res.status(500).json({ 
        message: 'Error performing AI analysis', 
        error: error.message 
      });
    }
  }

  // Get user's analysis history
  static async getUserAnalysisHistory(req, res, next) {
    try {
      const { userId } = req.params;
      const { limit = 10, page = 1 } = req.query;

      // Validate user
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Get journal entries with analysis
      const journalEntries = await JournalEntry.find({ userId })
        .sort({ entryDate: -1 })
        .limit(parseInt(limit))
        .skip((parseInt(page) - 1) * parseInt(limit));

      // Perform analysis on each entry
      const analysisHistory = await Promise.all(
        journalEntries.map(async (entry) => {
          const analysis = await AIAnalysisController.performAIAnalysis(entry.content);
          return {
            entryId: entry._id,
            date: entry.entryDate,
            content: entry.content,
            analysis,
          };
        })
      );

      res.status(200).json({
        message: 'Analysis history retrieved successfully',
        data: analysisHistory,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: await JournalEntry.countDocuments({ userId }),
        },
      });

    } catch (error) {
      console.error('Error getting analysis history:', error);
      res.status(500).json({ 
        message: 'Error retrieving analysis history', 
        error: error.message 
      });
    }
  }

  // Perform AI analysis using BERT model and enhanced dataset-based classification
  static async performAIAnalysis(content) {
    try {
      console.log('🔄 Starting comprehensive AI analysis...');

      // Get BERT model analysis (primary classification)
      const bertAnalysis = await bertModelService.analyzeText(content);
      console.log('✅ BERT analysis completed:', bertAnalysis.predicted_class);

      // Enhanced AI analysis using comprehensive datasets (secondary analysis)
      const enhancedClassification = AIAnalysisController.enhancedClassifyMentalHealth(content);
      const riskAssessment = AIAnalysisController.enhancedRiskAssessment(content);
      const contextualAnalysis = AIAnalysisController.performContextualAnalysis(content);

      // Combine BERT and rule-based analysis for more accurate results
      const combinedAnalysis = {
        // Primary classification from BERT model
        primaryClassification: {
          source: 'bert_model',
          predictedClass: bertAnalysis.predicted_class,
          confidence: bertAnalysis.confidence,
          classProbabilities: bertAnalysis.class_probabilities || {},
          isModelAvailable: bertModelService.isAvailable()
        },

        // Secondary classification from enhanced rules
        secondaryClassification: {
          source: 'enhanced_rules',
          primary: enhancedClassification.primary,
          severity: enhancedClassification.severity,
          confidence: enhancedClassification.confidence,
          scores: enhancedClassification.scores
        },

        // Final combined classification
        mentalHealthStatus: {
          primary: bertAnalysis.predicted_class || enhancedClassification.primary,
          severity: bertAnalysis.severity_assessment?.overall_severity || enhancedClassification.severity,
          confidence: Math.max(bertAnalysis.confidence || 0, enhancedClassification.confidence || 0),
          consensusScore: AIAnalysisController.calculateConsensusScore(bertAnalysis, enhancedClassification)
        },

        // Comprehensive analysis components
        sentimentScore: AIAnalysisController.calculateSentiment(content),
        emotionalIndicators: AIAnalysisController.extractEmotionalIndicators(content),
        riskLevel: riskAssessment,
        recommendations: AIAnalysisController.generateEnhancedRecommendations(content),
        keyThemes: AIAnalysisController.extractKeyThemes(content),
        confidenceScore: AIAnalysisController.calculateConfidenceScore(content),
        severityLevel: AIAnalysisController.assessSeverityLevel(content),
        contextualAnalysis: contextualAnalysis,

        // Analysis metadata
        analysisMetadata: {
          timestamp: new Date().toISOString(),
          textLength: content.length,
          wordCount: content.split(/\s+/).length,
          modelVersion: 'enhanced_v2.0',
          analysisType: 'hybrid_bert_rules'
        }
      };

      console.log('✅ Comprehensive AI analysis completed');
      return combinedAnalysis;

    } catch (error) {
      console.error('❌ Error in AI analysis:', error);

      // Fallback to rule-based analysis if BERT fails
      console.log('🔄 Falling back to rule-based analysis...');
      const fallbackAnalysis = {
        mentalHealthStatus: AIAnalysisController.enhancedClassifyMentalHealth(content),
        sentimentScore: AIAnalysisController.calculateSentiment(content),
        emotionalIndicators: AIAnalysisController.extractEmotionalIndicators(content),
        riskLevel: AIAnalysisController.enhancedRiskAssessment(content),
        recommendations: AIAnalysisController.generateEnhancedRecommendations(content),
        keyThemes: AIAnalysisController.extractKeyThemes(content),
        confidenceScore: AIAnalysisController.calculateConfidenceScore(content),
        severityLevel: AIAnalysisController.assessSeverityLevel(content),
        contextualAnalysis: AIAnalysisController.performContextualAnalysis(content),
        analysisMetadata: {
          timestamp: new Date().toISOString(),
          textLength: content.length,
          wordCount: content.split(/\s+/).length,
          modelVersion: 'fallback_v1.0',
          analysisType: 'rules_only',
          error: error.message
        }
      };

      return fallbackAnalysis;
    }
  }

  // Enhanced mental health classification using comprehensive datasets
  static enhancedClassifyMentalHealth(content) {
    const text = content.toLowerCase();
    const words = text.split(/\s+/);

    // Initialize scores for all categories
    let scores = {
      anxiety: { high: 0, medium: 0, low: 0 },
      depression: { high: 0, medium: 0, low: 0 },
      stress: { high: 0, medium: 0, low: 0 },
      positive: { high: 0, medium: 0, low: 0 },
      neutral: 0
    };

    // Check against comprehensive dataset patterns
    const datasets = [mentalHealthDataset, comprehensiveMentalHealthDataset, extendedMentalHealthDataset];

    datasets.forEach(dataset => {
      // Check anxiety patterns
      if (dataset.anxiety) {
        Object.keys(dataset.anxiety).forEach(level => {
          if (Array.isArray(dataset.anxiety[level])) {
            dataset.anxiety[level].forEach(example => {
              const similarity = AIAnalysisController.calculateTextSimilarity(text, example.toLowerCase());
              if (similarity > 0.3) {
                scores.anxiety[level] += similarity;
              }
            });
          }
        });
      }

      // Check depression patterns
      if (dataset.depression) {
        Object.keys(dataset.depression).forEach(level => {
          if (Array.isArray(dataset.depression[level])) {
            dataset.depression[level].forEach(example => {
              const similarity = AIAnalysisController.calculateTextSimilarity(text, example.toLowerCase());
              if (similarity > 0.3) {
                scores.depression[level] += similarity;
              }
            });
          }
        });
      }

      // Check stress patterns
      if (dataset.stress) {
        Object.keys(dataset.stress).forEach(level => {
          if (Array.isArray(dataset.stress[level])) {
            dataset.stress[level].forEach(example => {
              const similarity = AIAnalysisController.calculateTextSimilarity(text, example.toLowerCase());
              if (similarity > 0.3) {
                scores.stress[level] += similarity;
              }
            });
          }
        });
      }

      // Check positive patterns
      if (dataset.positive) {
        Object.keys(dataset.positive).forEach(level => {
          if (Array.isArray(dataset.positive[level])) {
            dataset.positive[level].forEach(example => {
              const similarity = AIAnalysisController.calculateTextSimilarity(text, example.toLowerCase());
              if (similarity > 0.3) {
                scores.positive[level] += similarity;
              }
            });
          }
        });
      }

      // Check neutral patterns
      if (dataset.neutral && Array.isArray(dataset.neutral)) {
        dataset.neutral.forEach(example => {
          const similarity = AIAnalysisController.calculateTextSimilarity(text, example.toLowerCase());
          if (similarity > 0.3) {
            scores.neutral += similarity;
          }
        });
      }
    });

    // Determine primary category and severity
    const categoryTotals = {
      anxiety: scores.anxiety.high + scores.anxiety.medium + scores.anxiety.low,
      depression: scores.depression.high + scores.depression.medium + scores.depression.low,
      stress: scores.stress.high + scores.stress.medium + scores.stress.low,
      positive: scores.positive.high + scores.positive.medium + scores.positive.low,
      neutral: scores.neutral
    };

    const primaryCategory = Object.keys(categoryTotals).reduce((a, b) =>
      categoryTotals[a] > categoryTotals[b] ? a : b
    );

    // Determine severity level for the primary category
    let severityLevel = 'low';
    if (scores[primaryCategory] && typeof scores[primaryCategory] === 'object') {
      const severityScores = scores[primaryCategory];
      if (severityScores.high > severityScores.medium && severityScores.high > severityScores.low) {
        severityLevel = 'high';
      } else if (severityScores.medium > severityScores.low) {
        severityLevel = 'medium';
      }
    }

    return {
      primary: primaryCategory,
      severity: severityLevel,
      scores: categoryTotals,
      detailedScores: scores,
      confidence: Math.min(categoryTotals[primaryCategory] / words.length, 1.0)
    };
  }

  // Calculate sentiment score
  static calculateSentiment(content) {
    const positiveWords = ['good', 'great', 'happy', 'love', 'amazing', 'wonderful', 'excellent'];
    const negativeWords = ['bad', 'terrible', 'hate', 'awful', 'horrible', 'sad', 'angry'];
    
    const words = content.toLowerCase().split(/\s+/);
    let score = 0;

    words.forEach(word => {
      if (positiveWords.includes(word)) score += 1;
      if (negativeWords.includes(word)) score -= 1;
    });

    return {
      score: score / words.length,
      label: score > 0 ? 'positive' : score < 0 ? 'negative' : 'neutral'
    };
  }

  // Extract emotional indicators
  static extractEmotionalIndicators(content) {
    const emotions = {
      joy: ['happy', 'joy', 'excited', 'cheerful', 'delighted'],
      sadness: ['sad', 'cry', 'tears', 'grief', 'sorrow'],
      anger: ['angry', 'mad', 'furious', 'rage', 'irritated'],
      fear: ['scared', 'afraid', 'terrified', 'anxious', 'worried'],
      surprise: ['surprised', 'shocked', 'amazed', 'astonished'],
      disgust: ['disgusted', 'revolted', 'sick', 'nauseated']
    };

    const detected = {};
    const text = content.toLowerCase();

    for (const [emotion, keywords] of Object.entries(emotions)) {
      const count = keywords.filter(keyword => text.includes(keyword)).length;
      if (count > 0) {
        detected[emotion] = count;
      }
    }

    return detected;
  }

  // Assess risk level
  static assessRiskLevel(content) {
    const highRiskKeywords = ['suicide', 'kill myself', 'end it all', 'no point', 'give up'];
    const mediumRiskKeywords = ['hopeless', 'worthless', 'can\'t go on', 'too much'];
    const lowRiskKeywords = ['stressed', 'tired', 'overwhelmed', 'difficult'];

    const text = content.toLowerCase();
    
    if (highRiskKeywords.some(keyword => text.includes(keyword))) {
      return { level: 'high', urgency: 'immediate', recommendation: 'Seek professional help immediately' };
    } else if (mediumRiskKeywords.some(keyword => text.includes(keyword))) {
      return { level: 'medium', urgency: 'soon', recommendation: 'Consider speaking with a counselor' };
    } else if (lowRiskKeywords.some(keyword => text.includes(keyword))) {
      return { level: 'low', urgency: 'monitor', recommendation: 'Practice self-care and monitor mood' };
    }
    
    return { level: 'minimal', urgency: 'none', recommendation: 'Continue healthy habits' };
  }

  // Generate recommendations
  static generateRecommendations(content) {
    const text = content.toLowerCase();
    const recommendations = [];

    if (text.includes('stress') || text.includes('overwhelmed')) {
      recommendations.push('Try deep breathing exercises or meditation');
      recommendations.push('Consider breaking tasks into smaller, manageable steps');
    }

    if (text.includes('sad') || text.includes('depressed')) {
      recommendations.push('Engage in physical activity or exercise');
      recommendations.push('Connect with friends or family members');
      recommendations.push('Consider professional counseling');
    }

    if (text.includes('anxious') || text.includes('worried')) {
      recommendations.push('Practice mindfulness or grounding techniques');
      recommendations.push('Limit caffeine intake');
      recommendations.push('Establish a regular sleep schedule');
    }

    if (recommendations.length === 0) {
      recommendations.push('Continue journaling to track your emotional well-being');
      recommendations.push('Maintain healthy lifestyle habits');
    }

    return recommendations;
  }

  // Extract key themes
  static extractKeyThemes(content) {
    const themes = {
      work: ['work', 'job', 'career', 'boss', 'colleague', 'office'],
      relationships: ['family', 'friend', 'partner', 'relationship', 'love', 'marriage'],
      health: ['health', 'sick', 'doctor', 'medicine', 'pain', 'tired'],
      finance: ['money', 'financial', 'debt', 'expensive', 'budget', 'income'],
      education: ['school', 'study', 'exam', 'grade', 'teacher', 'student']
    };

    const detected = {};
    const text = content.toLowerCase();

    for (const [theme, keywords] of Object.entries(themes)) {
      const count = keywords.filter(keyword => text.includes(keyword)).length;
      if (count > 0) {
        detected[theme] = count;
      }
    }

    return Object.keys(detected).sort((a, b) => detected[b] - detected[a]);
  }

  // Enhanced risk assessment using comprehensive risk keywords
  static enhancedRiskAssessment(content) {
    const text = content.toLowerCase();
    let riskScore = 0;
    let riskFactors = [];

    // Check against comprehensive risk keywords
    Object.keys(riskKeywords).forEach(level => {
      riskKeywords[level].forEach(keyword => {
        if (text.includes(keyword)) {
          const weight = level === 'high' ? 3 : level === 'medium' ? 2 : 1;
          riskScore += weight;
          riskFactors.push({ keyword, level, weight });
        }
      });
    });

    // Additional contextual risk assessment
    const contextualRisks = [
      { pattern: /can't (go on|continue|take it)/, weight: 2, level: 'medium' },
      { pattern: /want to (die|disappear|end)/, weight: 3, level: 'high' },
      { pattern: /no (hope|point|reason)/, weight: 2, level: 'medium' },
      { pattern: /everyone.*better.*without me/, weight: 3, level: 'high' },
      { pattern: /hurt myself/, weight: 3, level: 'high' }
    ];

    contextualRisks.forEach(risk => {
      if (risk.pattern.test(text)) {
        riskScore += risk.weight;
        riskFactors.push({ pattern: risk.pattern.source, level: risk.level, weight: risk.weight });
      }
    });

    // Determine risk level and recommendations
    let level, urgency, recommendation, actions;

    if (riskScore >= 6) {
      level = 'critical';
      urgency = 'immediate';
      recommendation = 'Seek emergency mental health services immediately';
      actions = ['Call emergency services', 'Contact crisis hotline', 'Go to nearest emergency room'];
    } else if (riskScore >= 4) {
      level = 'high';
      urgency = 'within 24 hours';
      recommendation = 'Contact a mental health professional immediately';
      actions = ['Schedule urgent therapy appointment', 'Contact your doctor', 'Reach out to trusted friend/family'];
    } else if (riskScore >= 2) {
      level = 'medium';
      urgency = 'within a week';
      recommendation = 'Consider speaking with a counselor or therapist';
      actions = ['Schedule therapy appointment', 'Practice self-care', 'Monitor mood changes'];
    } else if (riskScore >= 1) {
      level = 'low';
      urgency = 'monitor';
      recommendation = 'Practice self-care and monitor mood changes';
      actions = ['Engage in healthy activities', 'Connect with support system', 'Track mood patterns'];
    } else {
      level = 'minimal';
      urgency = 'none';
      recommendation = 'Continue healthy habits and self-care';
      actions = ['Maintain current wellness routine', 'Stay connected with others'];
    }

    return {
      level,
      urgency,
      recommendation,
      actions,
      riskScore,
      riskFactors,
      confidence: Math.min(riskScore / 10, 1.0)
    };
  }

  // Calculate text similarity using Jaccard similarity
  static calculateTextSimilarity(text1, text2) {
    const words1 = new Set(text1.split(/\s+/));
    const words2 = new Set(text2.split(/\s+/));

    const intersection = new Set([...words1].filter(x => words2.has(x)));
    const union = new Set([...words1, ...words2]);

    return intersection.size / union.size;
  }

  // Calculate confidence score based on multiple factors
  static calculateConfidenceScore(content) {
    const words = content.split(/\s+/);
    const wordCount = words.length;

    // Base confidence on text length (more text = higher confidence)
    let confidence = Math.min(wordCount / 50, 0.8);

    // Boost confidence if specific mental health terms are found
    const specificTerms = [
      ...contextPatterns.emotional.anxiety,
      ...contextPatterns.emotional.depression,
      ...contextPatterns.emotional.stress,
      ...contextPatterns.physical.anxiety,
      ...contextPatterns.physical.depression
    ];

    const foundTerms = specificTerms.filter(term =>
      content.toLowerCase().includes(term)
    ).length;

    confidence += (foundTerms * 0.05);

    return Math.min(confidence, 0.95);
  }

  // Assess severity level
  static assessSeverityLevel(content) {
    const text = content.toLowerCase();
    let severityScore = 0;

    // Check for severity indicators
    const severityIndicators = {
      mild: ['sometimes', 'occasionally', 'a little', 'slightly'],
      moderate: ['often', 'frequently', 'regularly', 'most days'],
      severe: ['always', 'constantly', 'every day', 'all the time', 'can\'t stop']
    };

    Object.keys(severityIndicators).forEach(level => {
      severityIndicators[level].forEach(indicator => {
        if (text.includes(indicator)) {
          severityScore += level === 'severe' ? 3 : level === 'moderate' ? 2 : 1;
        }
      });
    });

    if (severityScore >= 6) return 'severe';
    if (severityScore >= 3) return 'moderate';
    if (severityScore >= 1) return 'mild';
    return 'minimal';
  }

  // Perform contextual analysis using patterns
  static performContextualAnalysis(content) {
    const text = content.toLowerCase();
    const analysis = {
      physicalSymptoms: [],
      behavioralIndicators: [],
      cognitivePatterns: [],
      emotionalExpressions: []
    };

    // Check for physical symptoms
    Object.keys(contextPatterns.physical).forEach(condition => {
      contextPatterns.physical[condition].forEach(symptom => {
        if (text.includes(symptom)) {
          analysis.physicalSymptoms.push({ symptom, condition });
        }
      });
    });

    // Check for behavioral indicators
    Object.keys(contextPatterns.behavioral).forEach(condition => {
      contextPatterns.behavioral[condition].forEach(behavior => {
        if (text.includes(behavior)) {
          analysis.behavioralIndicators.push({ behavior, condition });
        }
      });
    });

    // Check for cognitive patterns
    Object.keys(contextPatterns.cognitive).forEach(condition => {
      contextPatterns.cognitive[condition].forEach(pattern => {
        if (text.includes(pattern)) {
          analysis.cognitivePatterns.push({ pattern, condition });
        }
      });
    });

    // Check for emotional expressions
    Object.keys(contextPatterns.emotional).forEach(condition => {
      contextPatterns.emotional[condition].forEach(emotion => {
        if (text.includes(emotion)) {
          analysis.emotionalExpressions.push({ emotion, condition });
        }
      });
    });

    return analysis;
  }

  // Generate enhanced recommendations based on comprehensive analysis
  static generateEnhancedRecommendations(content) {
    const text = content.toLowerCase();
    const recommendations = [];

    // Anxiety-specific recommendations
    if (text.includes('anxious') || text.includes('panic') || text.includes('worried')) {
      recommendations.push({
        category: 'anxiety',
        immediate: ['Practice deep breathing exercises', 'Try the 5-4-3-2-1 grounding technique'],
        shortTerm: ['Consider mindfulness meditation', 'Limit caffeine intake'],
        longTerm: ['Explore cognitive behavioral therapy', 'Build a regular exercise routine']
      });
    }

    // Depression-specific recommendations
    if (text.includes('sad') || text.includes('depressed') || text.includes('hopeless')) {
      recommendations.push({
        category: 'depression',
        immediate: ['Reach out to a trusted friend or family member', 'Engage in a small, enjoyable activity'],
        shortTerm: ['Establish a daily routine', 'Get sunlight exposure'],
        longTerm: ['Consider professional counseling', 'Join a support group']
      });
    }

    // Stress-specific recommendations
    if (text.includes('stress') || text.includes('overwhelmed') || text.includes('pressure')) {
      recommendations.push({
        category: 'stress',
        immediate: ['Take breaks throughout the day', 'Practice progressive muscle relaxation'],
        shortTerm: ['Prioritize tasks and delegate when possible', 'Improve sleep hygiene'],
        longTerm: ['Learn time management skills', 'Consider stress management counseling']
      });
    }

    // General wellness recommendations
    recommendations.push({
      category: 'general',
      immediate: ['Stay hydrated', 'Take a short walk'],
      shortTerm: ['Maintain social connections', 'Practice gratitude'],
      longTerm: ['Build resilience skills', 'Regular mental health check-ins']
    });

    return recommendations;
  }

  // Calculate consensus score between BERT and rule-based analysis
  static calculateConsensusScore(bertAnalysis, enhancedClassification) {
    try {
      const bertClass = bertAnalysis.predicted_class?.toLowerCase();
      const ruleClass = enhancedClassification.primary?.toLowerCase();

      // Perfect match
      if (bertClass === ruleClass) {
        return {
          score: 1.0,
          agreement: 'perfect',
          confidence: 'high'
        };
      }

      // Partial match (both indicate negative mental health)
      const negativeClasses = ['anxiety', 'depression', 'stress'];
      const bertIsNegative = negativeClasses.includes(bertClass);
      const ruleIsNegative = negativeClasses.includes(ruleClass);

      if (bertIsNegative && ruleIsNegative) {
        return {
          score: 0.7,
          agreement: 'partial',
          confidence: 'medium',
          note: 'Both methods detect mental health concerns'
        };
      }

      // Disagreement
      return {
        score: 0.3,
        agreement: 'disagreement',
        confidence: 'low',
        note: `BERT: ${bertClass}, Rules: ${ruleClass}`
      };

    } catch (error) {
      return {
        score: 0.5,
        agreement: 'unknown',
        confidence: 'low',
        error: error.message
      };
    }
  }
}

module.exports = AIAnalysisController;
