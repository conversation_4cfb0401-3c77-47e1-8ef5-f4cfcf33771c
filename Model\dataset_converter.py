#!/usr/bin/env python3
"""
Dataset Converter for MindEase AI Model
Converts JavaScript datasets to CSV format for BERT model training
"""

import pandas as pd
import json
import re
import os
from pathlib import Path

def load_js_dataset(file_path):
    """Load JavaScript dataset file and extract the data"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Extract the dataset object using regex
        # This is a simplified approach - you might need to adjust based on your JS structure
        dataset_match = re.search(r'const\s+\w+Dataset\s*=\s*({.*?});', content, re.DOTALL)
        if dataset_match:
            # Convert JS object to JSON-like format (simplified)
            js_object = dataset_match.group(1)
            # This is a basic conversion - you might need more sophisticated parsing
            return js_object
        return None
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def extract_examples_from_js_data():
    """Extract all examples from JavaScript datasets and create training data"""
    
    # Define the data structure for our training dataset
    training_data = []
    
    # Mental Health Dataset Examples
    mental_health_examples = {
        # Anxiety examples with severity levels
        'anxiety_high': [
            "I'm having panic attacks every day and can't breathe properly",
            "My heart is racing constantly and I feel like I'm going to die",
            "I can't leave my house anymore, everything feels terrifying",
            "I'm completely overwhelmed and feel like I'm losing control",
            "The anxiety is so bad I can't eat or sleep, I'm falling apart",
            "I feel like something catastrophic is about to happen all the time",
            "My chest feels tight and I'm hyperventilating frequently",
            "I'm terrified of everything and can't function normally",
            "The panic is so intense I went to the emergency room",
            "I feel like I'm trapped and there's no way out of this anxiety"
        ],
        'anxiety_medium': [
            "I feel anxious about upcoming presentations at work",
            "I worry constantly about my family's safety",
            "I get nervous in social situations and avoid them",
            "I have trouble sleeping because my mind races with worries",
            "I feel on edge most of the time and can't relax",
            "I worry about things that might never happen",
            "I feel restless and have difficulty concentrating",
            "I get anxious about making decisions, even small ones",
            "I worry about my health and check symptoms online frequently",
            "I feel tense and my muscles are always tight"
        ],
        'anxiety_low': [
            "I get a little nervous before job interviews",
            "I sometimes worry about my children's grades",
            "I feel slightly anxious about flying on airplanes",
            "I get nervous about public speaking occasionally",
            "I worry about being late to important appointments",
            "I feel a bit anxious about trying new things",
            "I get nervous about social media posts",
            "I worry about my pet's behavior problems",
            "I feel anxious about upcoming deadlines",
            "I get nervous about meeting new neighbors"
        ],
        
        # Depression examples with severity levels
        'depression_high': [
            "I feel completely hopeless and see no point in continuing",
            "I have no energy to do anything and feel worthless",
            "I've been thinking about ending my life lately",
            "Everything feels meaningless and I'm completely empty inside",
            "I can't get out of bed and feel like a burden to everyone",
            "I feel so alone and isolated, like nobody cares about me",
            "I've lost interest in everything I used to enjoy",
            "I feel like I'm drowning in sadness and despair",
            "I can't stop crying and feel completely broken",
            "I feel numb and disconnected from everything around me"
        ],
        'depression_medium': [
            "I've been feeling really sad and down for weeks now",
            "I don't enjoy things like I used to and feel unmotivated",
            "I feel tired all the time and have no energy",
            "I've been isolating myself from friends and family",
            "I feel lonely even when I'm around other people",
            "I have trouble concentrating and making decisions",
            "I feel like I'm just going through the motions of life",
            "I've been sleeping too much or having trouble sleeping",
            "I feel pessimistic about the future and my prospects",
            "I don't feel like myself anymore and everything seems hard"
        ],
        'depression_low': [
            "I've been feeling a bit down lately",
            "I don't have as much energy as usual",
            "I feel sad sometimes but it passes",
            "I've been less interested in my hobbies recently",
            "I feel a little lonely but I'm managing",
            "I've been more tired than usual lately",
            "I feel a bit unmotivated but I'm still functioning",
            "I've been having some sad thoughts but they're manageable",
            "I feel less enthusiastic about things I usually enjoy",
            "I've been feeling a bit blue but it's not overwhelming"
        ],
        
        # Stress examples
        'stress_high': [
            "I'm at my breaking point and can't handle any more pressure",
            "I feel like I'm drowning in responsibilities and deadlines",
            "The stress is making me physically sick and I can't cope",
            "I'm completely burned out and exhausted from everything",
            "I feel like I'm going to have a breakdown from all this stress",
            "I can't sleep, eat, or think clearly because of the pressure",
            "Everything is falling apart and I can't keep up anymore",
            "I feel like I'm suffocating under all these demands",
            "The stress is affecting my health and relationships badly",
            "I'm overwhelmed to the point where I can't function"
        ],
        'stress_medium': [
            "I feel overwhelmed with work and personal responsibilities",
            "I'm having trouble managing all my commitments",
            "I feel pressured and stressed most days",
            "I'm struggling to balance everything in my life",
            "I feel like there's too much on my plate right now",
            "I'm stressed about meeting all my deadlines",
            "I feel tense and worried about my workload",
            "I'm having difficulty coping with daily pressures",
            "I feel stretched thin and need a break",
            "I'm stressed about financial and work pressures"
        ],
        
        # Positive mental health examples
        'positive_high': [
            "I feel absolutely amazing and full of energy today",
            "I'm so grateful for all the wonderful things in my life",
            "I feel incredibly happy and optimistic about the future",
            "I'm bursting with joy and excitement about new opportunities",
            "I feel fantastic and like I can accomplish anything",
            "I'm filled with love and appreciation for everyone around me",
            "I feel so alive and vibrant, like I'm glowing from within",
            "I'm experiencing pure bliss and contentment right now",
            "I feel incredibly blessed and fortunate in every way",
            "I'm radiating positivity and feel unstoppable"
        ],
        'positive_medium': [
            "I'm feeling pretty good and optimistic today",
            "I feel content and satisfied with my life",
            "I'm in a good mood and feeling positive",
            "I feel happy and grateful for what I have",
            "I'm feeling confident and capable",
            "I feel peaceful and balanced",
            "I'm enjoying life and feeling fulfilled",
            "I feel hopeful about the future",
            "I'm feeling energetic and motivated",
            "I feel good about myself and my relationships"
        ],
        
        # Neutral examples
        'neutral': [
            "I went to work today and had a normal day",
            "I'm feeling neither good nor bad, just regular",
            "Today was an average day with nothing special happening",
            "I feel normal and my mood is stable",
            "I'm doing my usual routine and feeling okay",
            "I feel fine, nothing particularly exciting or concerning",
            "I'm in a neutral mood and going about my day",
            "I feel balanced and my emotions are steady",
            "I'm feeling normal and content with my routine",
            "I feel okay and my day was pretty typical"
        ]
    }
    
    # Convert to training format
    for category, examples in mental_health_examples.items():
        # Map categories to labels
        if 'anxiety' in category:
            label = 'Anxiety'
        elif 'depression' in category:
            label = 'Depression'
        elif 'stress' in category:
            label = 'Stress'
        elif 'positive' in category:
            label = 'Positive'
        else:
            label = 'Neutral'
            
        for example in examples:
            training_data.append({
                'statement': example,
                'status': label,
                'severity': category.split('_')[-1] if '_' in category else 'medium',
                'category_detail': category
            })
    
    return training_data

def create_enhanced_dataset():
    """Create an enhanced dataset combining original and new data"""
    
    print("🔄 Creating enhanced mental health dataset...")
    
    # Load original dataset if it exists
    original_data = []
    original_csv_path = Path("Combined Data.csv")
    if original_csv_path.exists():
        try:
            original_df = pd.read_csv(original_csv_path)
            original_data = original_df.to_dict('records')
            print(f"✅ Loaded {len(original_data)} examples from original dataset")
        except Exception as e:
            print(f"⚠️ Could not load original dataset: {e}")
    
    # Extract new examples from JS datasets
    new_data = extract_examples_from_js_data()
    print(f"✅ Generated {len(new_data)} examples from enhanced datasets")
    
    # Combine datasets
    all_data = original_data + new_data
    
    # Create DataFrame
    df = pd.DataFrame(all_data)
    
    # Clean and standardize the data
    df = df.dropna()
    df['statement'] = df['statement'].str.strip()
    
    # Remove duplicates
    df = df.drop_duplicates(subset=['statement'])
    
    # Save enhanced dataset
    output_path = "Enhanced_Mental_Health_Dataset.csv"
    df.to_csv(output_path, index=False)
    
    print(f"✅ Enhanced dataset saved to {output_path}")
    print(f"📊 Total examples: {len(df)}")
    print(f"📊 Categories: {df['status'].value_counts().to_dict()}")
    
    return df

def create_bert_training_script():
    """Create a Python script for BERT model training with the enhanced dataset"""
    
    script_content = '''#!/usr/bin/env python3
"""
Enhanced BERT Training Script for MindEase Mental Health Classification
Uses the enhanced dataset with comprehensive mental health examples
"""

import pandas as pd
import numpy as np
import torch
from torch.utils.data import DataLoader
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import confusion_matrix, classification_report
from transformers import (
    BertTokenizer, 
    BertForSequenceClassification, 
    Trainer, 
    TrainingArguments,
    AutoModelForSequenceClassification,
    AutoTokenizer
)
from datasets import Dataset
import pickle
import re
from nltk.corpus import stopwords
import nltk

# Download required NLTK data
try:
    nltk.download('stopwords', quiet=True)
    stopwords_set = set(stopwords.words("english"))
except:
    stopwords_set = set()

def preprocessing(text):
    """Enhanced text preprocessing"""
    text = text.lower()
    text = re.sub(r"[^\\w\\s]", "", text)
    text = re.sub(r"\\d+", "", text)
    words = text.split()
    words = [w for w in words if w not in stopwords_set and len(w) > 2]
    return " ".join(words)

def load_and_prepare_data():
    """Load and prepare the enhanced dataset"""
    print("📊 Loading enhanced dataset...")
    
    # Load the enhanced dataset
    df = pd.read_csv("Enhanced_Mental_Health_Dataset.csv")
    print(f"✅ Loaded {len(df)} examples")
    
    # Clean and preprocess
    df = df.dropna()
    df['cleaned_text'] = df['statement'].apply(preprocessing)
    df['statement_length'] = df['statement'].apply(len)
    df['cleaned_text_length'] = df['cleaned_text'].apply(len)
    
    # Remove very short texts
    df = df[df['cleaned_text_length'] > 5]
    
    print(f"📊 After cleaning: {len(df)} examples")
    print(f"📊 Categories: {df['status'].value_counts().to_dict()}")
    
    return df

def train_enhanced_bert_model():
    """Train BERT model with enhanced dataset"""
    
    # Load and prepare data
    df = load_and_prepare_data()
    
    # Encode labels
    encode = LabelEncoder()
    df['status_encoded'] = encode.fit_transform(df['status'])
    
    # Prepare features and labels
    X = df['statement']
    y = df['status_encoded']
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    print(f"🔄 Training set: {len(X_train)} examples")
    print(f"🔄 Test set: {len(X_test)} examples")
    
    # Initialize tokenizer and model
    tokenizer = BertTokenizer.from_pretrained("bert-base-uncased")
    num_labels = len(df['status'].unique())
    model = BertForSequenceClassification.from_pretrained(
        "bert-base-uncased", 
        num_labels=num_labels
    )
    
    # Tokenize data
    print("🔄 Tokenizing data...")
    train_encodings = tokenizer(
        list(X_train), 
        padding=True, 
        truncation=True, 
        max_length=256,
        return_tensors="pt"
    )
    test_encodings = tokenizer(
        list(X_test), 
        padding=True, 
        truncation=True, 
        max_length=256,
        return_tensors="pt"
    )
    
    # Create datasets
    train_dataset = Dataset.from_dict({
        "input_ids": train_encodings["input_ids"],
        "attention_mask": train_encodings["attention_mask"],
        "labels": y_train.tolist()
    })
    
    test_dataset = Dataset.from_dict({
        "input_ids": test_encodings["input_ids"],
        "attention_mask": test_encodings["attention_mask"],
        "labels": y_test.tolist()
    })
    
    # Training arguments
    training_args = TrainingArguments(
        output_dir="./enhanced_mental_health_bert",
        evaluation_strategy="epoch",
        save_strategy="epoch",
        learning_rate=2e-5,
        per_device_train_batch_size=16,
        per_device_eval_batch_size=16,
        num_train_epochs=5,
        weight_decay=0.01,
        logging_dir="./logs",
        logging_steps=10,
        lr_scheduler_type="linear",
        warmup_steps=500,
        load_best_model_at_end=True,
        metric_for_best_model="eval_loss",
        save_total_limit=3,
        gradient_accumulation_steps=2,
        report_to="none"
    )
    
    # Initialize trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=test_dataset
    )
    
    # Train the model
    print("🚀 Starting training...")
    trainer.train()
    
    # Evaluate the model
    print("📊 Evaluating model...")
    predictions, labels, _ = trainer.predict(test_dataset)
    pred_labels = np.argmax(predictions, axis=1)
    
    # Print results
    print("\\n📊 Classification Report:")
    print(classification_report(labels, pred_labels, target_names=encode.classes_))
    
    # Save model and tokenizer
    print("💾 Saving model...")
    trainer.save_model("enhanced_mental_health_bert")
    tokenizer.save_pretrained("enhanced_mental_health_bert")
    
    # Save label encoder
    with open("enhanced_encode.pkl", "wb") as f:
        pickle.dump(encode, f)
    
    print("✅ Training completed successfully!")
    print("📁 Model saved to: enhanced_mental_health_bert/")
    print("📁 Label encoder saved to: enhanced_encode.pkl")
    
    return trainer, tokenizer, encode

if __name__ == "__main__":
    train_enhanced_bert_model()
'''
    
    with open("enhanced_bert_training.py", "w", encoding="utf-8") as f:
        f.write(script_content)
    
    print("✅ Enhanced BERT training script created: enhanced_bert_training.py")

if __name__ == "__main__":
    print("🚀 MindEase Dataset Converter")
    print("=" * 50)
    
    # Create enhanced dataset
    df = create_enhanced_dataset()
    
    # Create BERT training script
    create_bert_training_script()
    
    print("\n✅ Dataset conversion completed!")
    print("📋 Next steps:")
    print("1. Run: python enhanced_bert_training.py")
    print("2. This will train a new BERT model with your enhanced dataset")
    print("3. The trained model will be more accurate for English mental health text")
