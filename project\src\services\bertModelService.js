/**
 * BERT Model Service for MindEase Mental Health Analysis
 * Integrates the enhanced BERT model with the Node.js backend
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

class BertModelService {
  constructor() {
    this.modelPath = path.join(__dirname, '../../../model/enhanced_mental_health_bert');
    this.encoderPath = path.join(__dirname, '../../../model/enhanced_encode.pkl');
    this.pythonScriptPath = path.join(__dirname, '../../../model/bert_inference.py');
    this.isModelLoaded = false;
    this.initializeModel();
  }

  /**
   * Initialize the BERT model service
   */
  async initializeModel() {
    try {
      // Check if model files exist
      if (!fs.existsSync(this.modelPath)) {
        console.log('⚠️ Enhanced BERT model not found. Using fallback analysis.');
        return;
      }

      // Create Python inference script if it doesn't exist
      if (!fs.existsSync(this.pythonScriptPath)) {
        await this.createInferenceScript();
      }

      this.isModelLoaded = true;
      console.log('✅ BERT Model Service initialized successfully');
    } catch (error) {
      console.error('❌ Error initializing BERT model:', error);
      this.isModelLoaded = false;
    }
  }

  /**
   * Create Python inference script for BERT model
   */
  async createInferenceScript() {
    const inferenceScript = `#!/usr/bin/env python3
"""
BERT Model Inference Script for MindEase
Provides mental health text classification using the enhanced BERT model
"""

import sys
import json
import torch
import pickle
import re
from transformers import AutoModelForSequenceClassification, AutoTokenizer
from nltk.corpus import stopwords
import nltk

# Download stopwords if not available
try:
    nltk.download('stopwords', quiet=True)
    stopwords_set = set(stopwords.words("english"))
except:
    stopwords_set = set()

class MentalHealthBertClassifier:
    def __init__(self, model_path, encoder_path):
        self.model_path = model_path
        self.encoder_path = encoder_path
        self.model = None
        self.tokenizer = None
        self.encoder = None
        self.load_model()
    
    def load_model(self):
        """Load the trained BERT model and tokenizer"""
        try:
            self.model = AutoModelForSequenceClassification.from_pretrained(self.model_path)
            self.tokenizer = AutoTokenizer.from_pretrained(self.model_path)
            
            with open(self.encoder_path, 'rb') as f:
                self.encoder = pickle.load(f)
            
            self.model.eval()
            print("✅ BERT model loaded successfully", file=sys.stderr)
        except Exception as e:
            print(f"❌ Error loading model: {e}", file=sys.stderr)
            raise e
    
    def preprocess_text(self, text):
        """Preprocess text for BERT input"""
        text = text.lower()
        text = re.sub(r"[^\\w\\s]", "", text)
        text = re.sub(r"\\d+", "", text)
        words = text.split()
        words = [w for w in words if w not in stopwords_set and len(w) > 2]
        return " ".join(words)
    
    def predict(self, text):
        """Predict mental health status for given text"""
        try:
            # Preprocess text
            cleaned_text = self.preprocess_text(text)
            
            # Tokenize
            inputs = self.tokenizer(
                cleaned_text,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=256
            )
            
            # Get prediction
            with torch.no_grad():
                outputs = self.model(**inputs)
                logits = outputs.logits
                probabilities = torch.softmax(logits, dim=1)
                predicted_class = torch.argmax(logits, dim=1).item()
                confidence = torch.max(probabilities).item()
            
            # Convert to label
            predicted_label = self.encoder.inverse_transform([predicted_class])[0]
            
            # Get all class probabilities
            class_probabilities = {}
            for i, class_name in enumerate(self.encoder.classes_):
                class_probabilities[class_name] = float(probabilities[0][i])
            
            return {
                'predicted_class': predicted_label,
                'confidence': float(confidence),
                'class_probabilities': class_probabilities,
                'processed_text': cleaned_text
            }
            
        except Exception as e:
            print(f"❌ Error in prediction: {e}", file=sys.stderr)
            return {
                'error': str(e),
                'predicted_class': 'Neutral',
                'confidence': 0.0
            }

def main():
    if len(sys.argv) != 4:
        print("Usage: python bert_inference.py <model_path> <encoder_path> <text>", file=sys.stderr)
        sys.exit(1)
    
    model_path = sys.argv[1]
    encoder_path = sys.argv[2]
    text = sys.argv[3]
    
    try:
        classifier = MentalHealthBertClassifier(model_path, encoder_path)
        result = classifier.predict(text)
        print(json.dumps(result))
    except Exception as e:
        error_result = {
            'error': str(e),
            'predicted_class': 'Neutral',
            'confidence': 0.0
        }
        print(json.dumps(error_result))

if __name__ == "__main__":
    main()
`;

    fs.writeFileSync(this.pythonScriptPath, inferenceScript);
    console.log('✅ Python inference script created');
  }

  /**
   * Analyze text using the BERT model
   * @param {string} text - Text to analyze
   * @returns {Promise<Object>} Analysis result
   */
  async analyzeText(text) {
    if (!this.isModelLoaded) {
      console.log('⚠️ BERT model not available, using fallback analysis');
      return this.fallbackAnalysis(text);
    }

    return new Promise((resolve, reject) => {
      const pythonProcess = spawn('python3', [
        this.pythonScriptPath,
        this.modelPath,
        this.encoderPath,
        text
      ]);

      let output = '';
      let errorOutput = '';

      pythonProcess.stdout.on('data', (data) => {
        output += data.toString();
      });

      pythonProcess.stderr.on('data', (data) => {
        errorOutput += data.toString();
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(output.trim());
            resolve(this.enhanceAnalysisResult(result, text));
          } catch (error) {
            console.error('❌ Error parsing BERT output:', error);
            resolve(this.fallbackAnalysis(text));
          }
        } else {
          console.error('❌ Python process error:', errorOutput);
          resolve(this.fallbackAnalysis(text));
        }
      });

      pythonProcess.on('error', (error) => {
        console.error('❌ Failed to start Python process:', error);
        resolve(this.fallbackAnalysis(text));
      });
    });
  }

  /**
   * Enhance BERT analysis result with additional insights
   * @param {Object} bertResult - Result from BERT model
   * @param {string} originalText - Original input text
   * @returns {Object} Enhanced analysis result
   */
  enhanceAnalysisResult(bertResult, originalText) {
    const enhanced = {
      ...bertResult,
      source: 'bert_model',
      analysis_timestamp: new Date().toISOString(),
      text_length: originalText.length,
      word_count: originalText.split(/\\s+/).length
    };

    // Add severity assessment based on confidence and class probabilities
    if (bertResult.class_probabilities) {
      const anxietyScore = bertResult.class_probabilities.Anxiety || 0;
      const depressionScore = bertResult.class_probabilities.Depression || 0;
      const stressScore = bertResult.class_probabilities.Stress || 0;

      enhanced.severity_assessment = {
        anxiety_level: this.assessSeverity(anxietyScore),
        depression_level: this.assessSeverity(depressionScore),
        stress_level: this.assessSeverity(stressScore),
        overall_severity: this.assessOverallSeverity(bertResult.predicted_class, bertResult.confidence)
      };
    }

    return enhanced;
  }

  /**
   * Assess severity level based on probability score
   * @param {number} score - Probability score (0-1)
   * @returns {string} Severity level
   */
  assessSeverity(score) {
    if (score >= 0.8) return 'high';
    if (score >= 0.6) return 'medium';
    if (score >= 0.3) return 'low';
    return 'minimal';
  }

  /**
   * Assess overall severity based on predicted class and confidence
   * @param {string} predictedClass - Predicted mental health class
   * @param {number} confidence - Confidence score
   * @returns {string} Overall severity
   */
  assessOverallSeverity(predictedClass, confidence) {
    if (predictedClass === 'Positive' || predictedClass === 'Neutral') {
      return 'minimal';
    }

    if (confidence >= 0.9) return 'high';
    if (confidence >= 0.7) return 'medium';
    if (confidence >= 0.5) return 'low';
    return 'minimal';
  }

  /**
   * Fallback analysis when BERT model is not available
   * @param {string} text - Text to analyze
   * @returns {Object} Fallback analysis result
   */
  fallbackAnalysis(text) {
    const lowerText = text.toLowerCase();
    
    // Simple keyword-based classification
    const anxietyKeywords = ['anxious', 'panic', 'worried', 'nervous', 'fear', 'stress'];
    const depressionKeywords = ['sad', 'depressed', 'hopeless', 'empty', 'worthless', 'lonely'];
    const positiveKeywords = ['happy', 'joy', 'excited', 'grateful', 'confident', 'peaceful'];
    
    let scores = { Anxiety: 0, Depression: 0, Positive: 0, Neutral: 0 };
    
    anxietyKeywords.forEach(keyword => {
      if (lowerText.includes(keyword)) scores.Anxiety++;
    });
    
    depressionKeywords.forEach(keyword => {
      if (lowerText.includes(keyword)) scores.Depression++;
    });
    
    positiveKeywords.forEach(keyword => {
      if (lowerText.includes(keyword)) scores.Positive++;
    });
    
    const maxScore = Math.max(...Object.values(scores));
    const predictedClass = Object.keys(scores).find(key => scores[key] === maxScore) || 'Neutral';
    const confidence = maxScore > 0 ? Math.min(maxScore / text.split(' ').length * 5, 0.8) : 0.3;
    
    return {
      predicted_class: predictedClass,
      confidence: confidence,
      class_probabilities: scores,
      source: 'fallback_analysis',
      analysis_timestamp: new Date().toISOString()
    };
  }

  /**
   * Check if BERT model is available
   * @returns {boolean} Model availability status
   */
  isAvailable() {
    return this.isModelLoaded;
  }

  /**
   * Get model information
   * @returns {Object} Model information
   */
  getModelInfo() {
    return {
      isLoaded: this.isModelLoaded,
      modelPath: this.modelPath,
      encoderPath: this.encoderPath,
      pythonScriptPath: this.pythonScriptPath
    };
  }
}

// Create singleton instance
const bertModelService = new BertModelService();

module.exports = bertModelService;
